﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace AFWifiServerDemo.entitys
{
    /// <summary>
    /// Exam_Student
    /// </summary>
    [Table("Exam_Student")]
    [SugarTable("Exam_Student")]
    public class Exam_Student
    {
        public string ClassId { get; set; }

        /// <summary>
        /// Id
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, ColumnName = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public String CreatorId { get; set; }

        /// <summary>
        /// 否已删除
        /// </summary>
        public Boolean Deleted { get; set; }

        /// <summary>
        /// UID
        /// </summary>
        public Guid? UID { get; set; }

        /// <summary>
        /// UserName
        /// </summary>
        public String UserName { get; set; }

        /// <summary>
        /// RealName
        /// </summary>
        public String RealName { get; set; }

        /// <summary>
        /// NickName
        /// </summary>
        public String NickName { get; set; }

        /// <summary>
        /// UserPinyin
        /// </summary>
        public String UserPinyin { get; set; }

        /// <summary>
        /// Password
        /// </summary>
        public String Password { get; set; }

        /// <summary>
        /// Birthday
        /// </summary>
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// Gender
        /// </summary>
        public Int32? Gender { get; set; }

        /// <summary>
        /// 年级
        /// </summary>
        public Int32? Grade { get; set; }

        /// <summary>
        /// UserType
        /// </summary>
        public Int32? UserType { get; set; }

        /// <summary>
        /// UserState
        /// </summary>
        public Int32? UserState { get; set; }

        /// <summary>
        /// SchoolId
        /// </summary>
        public string SchoolId { get; set; }

        /// <summary>
        /// RegistTime
        /// </summary>
        public DateTime? RegistTime { get; set; }

        /// <summary>
        /// PhoneNum
        /// </summary>
        public String PhoneNum { get; set; }

        /// <summary>
        /// StudentNo
        /// </summary>
        public String StudentNo { get; set; }

        /// <summary>
        /// CodeId
        /// </summary>
        public String CodeId { get; set; }

        /// <summary>
        /// Reason
        /// </summary>
        public String Reason { get; set; }

        /// <summary>
        /// 统一身份认证Id
        /// </summary>
        public String UnionId { get; set; }

        /// <summary>
        /// 微校网校用户认证 Id
        /// </summary>
        public string WxId { get; set; }
        /// <summary>
        /// 徐汇网校认证Id
        /// </summary>
        public string XhId { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public String Photo { get; set; }

        /// <summary>
        /// 区ID
        /// </summary>
        public int? SAreaid { get; set; }

        /// <summary>
        /// 目标奖状
        /// </summary>
        public string UserTarget { get; set; }

        /// <summary>
        /// 注销时间
        /// </summary>
        public string LogoutTime { get; set; }

        /// <summary>
        /// 是否是三个助手学生（1是、0否）
        /// </summary>
        public int? IsTY { get; set; }

        /// <summary>
        /// 是否是专课专练学生（1是、0否）
        /// </summary>
        public int? IsZKZL { get; set; }

        /// <summary>
        /// 是否是好学效学生（1是、0否）
        /// </summary>
        public int? IsHXX { get; set; }

        /// <summary>
        /// 黄埔ID
        /// </summary>
        public string HpId { get; set; }

    }
}
