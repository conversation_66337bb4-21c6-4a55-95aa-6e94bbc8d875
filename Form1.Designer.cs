﻿namespace AFWifiServerDemo
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            buttonPlayURL = new Button();
            textBoxPlayURL = new TextBox();
            listBox1 = new ListBox();
            buttonRStart = new Button();
            buttonRStop = new Button();
            buttonPRecord = new Button();
            textBoxRecordNum = new TextBox();
            buttonGetPlayStatus = new Button();
            buttonGetRCount = new Button();
            buttonGetRStatus = new Button();
            panel1 = new Panel();
            textBoxRAllowBtn = new TextBox();
            labelRGetAllownBtn = new Label();
            buttonRGetAllowBtn = new Button();
            buttonRSetAllowBtn = new Button();
            buttonRGetMaxDur = new Button();
            textBoxRMaxDur = new TextBox();
            labelRGetDur = new Label();
            buttonRSetMaxDur = new Button();
            labelVolume = new Label();
            buttonPGetVolume = new Button();
            labelPlayStatus = new Label();
            labelRecordStatus = new Label();
            labelRCnt = new Label();
            buttonRDownload = new Button();
            buttonRErase = new Button();
            buttonPVolumeDown = new Button();
            buttonPVolumeUp = new Button();
            buttonPResume = new Button();
            buttonPStop = new Button();
            buttonPPause = new Button();
            panel2 = new Panel();
            label1 = new Label();
            label2 = new Label();
            panel1.SuspendLayout();
            SuspendLayout();
            // 
            // buttonPlayURL
            // 
            buttonPlayURL.Location = new Point(4, 4);
            buttonPlayURL.Margin = new Padding(4);
            buttonPlayURL.Name = "buttonPlayURL";
            buttonPlayURL.Size = new Size(153, 29);
            buttonPlayURL.TabIndex = 0;
            buttonPlayURL.Text = "PlayURL";
            buttonPlayURL.UseVisualStyleBackColor = true;
            buttonPlayURL.Click += buttonPlayURL_Click;
            // 
            // textBoxPlayURL
            // 
            textBoxPlayURL.Location = new Point(162, 5);
            textBoxPlayURL.Margin = new Padding(4);
            textBoxPlayURL.Name = "textBoxPlayURL";
            textBoxPlayURL.Size = new Size(358, 27);
            textBoxPlayURL.TabIndex = 1;
            // 
            // listBox1
            // 
            listBox1.FormattingEnabled = true;
            listBox1.ItemHeight = 19;
            listBox1.Location = new Point(28, 94);
            listBox1.Margin = new Padding(4);
            listBox1.Name = "listBox1";
            listBox1.SelectionMode = SelectionMode.MultiExtended;
            listBox1.Size = new Size(199, 156);
            listBox1.TabIndex = 3;
            listBox1.SizeChanged += listBox1_SizeChanged;
            // 
            // buttonRStart
            // 
            buttonRStart.Location = new Point(4, 327);
            buttonRStart.Margin = new Padding(4);
            buttonRStart.Name = "buttonRStart";
            buttonRStart.Size = new Size(153, 29);
            buttonRStart.TabIndex = 4;
            buttonRStart.Text = "Record Start";
            buttonRStart.UseVisualStyleBackColor = true;
            buttonRStart.Click += buttonRStart_Click;
            // 
            // buttonRStop
            // 
            buttonRStop.Location = new Point(4, 364);
            buttonRStop.Margin = new Padding(4);
            buttonRStop.Name = "buttonRStop";
            buttonRStop.Size = new Size(153, 29);
            buttonRStop.TabIndex = 5;
            buttonRStop.Text = "Record Stop";
            buttonRStop.UseVisualStyleBackColor = true;
            buttonRStop.Click += buttonRStop_Click;
            // 
            // buttonPRecord
            // 
            buttonPRecord.Location = new Point(4, 41);
            buttonPRecord.Margin = new Padding(4);
            buttonPRecord.Name = "buttonPRecord";
            buttonPRecord.Size = new Size(153, 29);
            buttonPRecord.TabIndex = 6;
            buttonPRecord.Text = "PlayRecordNum";
            buttonPRecord.UseVisualStyleBackColor = true;
            buttonPRecord.Click += buttonPRecord_Click;
            // 
            // textBoxRecordNum
            // 
            textBoxRecordNum.Location = new Point(162, 41);
            textBoxRecordNum.Margin = new Padding(4);
            textBoxRecordNum.Name = "textBoxRecordNum";
            textBoxRecordNum.Size = new Size(48, 27);
            textBoxRecordNum.TabIndex = 7;
            textBoxRecordNum.Text = "1";
            // 
            // buttonGetPlayStatus
            // 
            buttonGetPlayStatus.Location = new Point(4, 77);
            buttonGetPlayStatus.Margin = new Padding(4);
            buttonGetPlayStatus.Name = "buttonGetPlayStatus";
            buttonGetPlayStatus.Size = new Size(153, 29);
            buttonGetPlayStatus.TabIndex = 8;
            buttonGetPlayStatus.Text = "GetPlayStatus";
            buttonGetPlayStatus.UseVisualStyleBackColor = true;
            buttonGetPlayStatus.Click += buttonGetPlayStatus_Click;
            // 
            // buttonGetRCount
            // 
            buttonGetRCount.Location = new Point(4, 400);
            buttonGetRCount.Margin = new Padding(4);
            buttonGetRCount.Name = "buttonGetRCount";
            buttonGetRCount.Size = new Size(153, 29);
            buttonGetRCount.TabIndex = 10;
            buttonGetRCount.Text = "Get Record Cnt";
            buttonGetRCount.UseVisualStyleBackColor = true;
            buttonGetRCount.Click += buttonGetRCount_Click;
            // 
            // buttonGetRStatus
            // 
            buttonGetRStatus.Location = new Point(4, 437);
            buttonGetRStatus.Margin = new Padding(4);
            buttonGetRStatus.Name = "buttonGetRStatus";
            buttonGetRStatus.Size = new Size(153, 29);
            buttonGetRStatus.TabIndex = 12;
            buttonGetRStatus.Text = "Get Record Status";
            buttonGetRStatus.UseVisualStyleBackColor = true;
            buttonGetRStatus.Click += buttonGetRStatus_Click;
            // 
            // panel1
            // 
            panel1.Controls.Add(label2);
            panel1.Controls.Add(textBoxRAllowBtn);
            panel1.Controls.Add(labelRGetAllownBtn);
            panel1.Controls.Add(buttonRGetAllowBtn);
            panel1.Controls.Add(buttonRSetAllowBtn);
            panel1.Controls.Add(buttonRGetMaxDur);
            panel1.Controls.Add(textBoxRMaxDur);
            panel1.Controls.Add(labelRGetDur);
            panel1.Controls.Add(buttonRSetMaxDur);
            panel1.Controls.Add(labelVolume);
            panel1.Controls.Add(buttonPGetVolume);
            panel1.Controls.Add(labelPlayStatus);
            panel1.Controls.Add(labelRecordStatus);
            panel1.Controls.Add(labelRCnt);
            panel1.Controls.Add(buttonRDownload);
            panel1.Controls.Add(buttonRErase);
            panel1.Controls.Add(buttonPVolumeDown);
            panel1.Controls.Add(buttonPVolumeUp);
            panel1.Controls.Add(buttonPResume);
            panel1.Controls.Add(buttonPStop);
            panel1.Controls.Add(buttonPPause);
            panel1.Controls.Add(buttonPlayURL);
            panel1.Controls.Add(buttonGetRStatus);
            panel1.Controls.Add(textBoxPlayURL);
            panel1.Controls.Add(buttonRStart);
            panel1.Controls.Add(buttonGetRCount);
            panel1.Controls.Add(buttonRStop);
            panel1.Controls.Add(buttonPRecord);
            panel1.Controls.Add(buttonGetPlayStatus);
            panel1.Controls.Add(textBoxRecordNum);
            panel1.Location = new Point(253, 15);
            panel1.Margin = new Padding(4);
            panel1.Name = "panel1";
            panel1.Size = new Size(621, 554);
            panel1.TabIndex = 13;
            // 
            // textBoxRAllowBtn
            // 
            textBoxRAllowBtn.Location = new Point(414, 474);
            textBoxRAllowBtn.Name = "textBoxRAllowBtn";
            textBoxRAllowBtn.Size = new Size(57, 27);
            textBoxRAllowBtn.TabIndex = 33;
            // 
            // labelRGetAllownBtn
            // 
            labelRGetAllownBtn.AutoSize = true;
            labelRGetAllownBtn.Location = new Point(420, 511);
            labelRGetAllownBtn.Margin = new Padding(4, 0, 4, 0);
            labelRGetAllownBtn.Name = "labelRGetAllownBtn";
            labelRGetAllownBtn.Size = new Size(51, 19);
            labelRGetAllownBtn.TabIndex = 32;
            labelRGetAllownBtn.Text = "label1";
            // 
            // buttonRGetAllowBtn
            // 
            buttonRGetAllowBtn.Location = new Point(224, 501);
            buttonRGetAllowBtn.Margin = new Padding(4);
            buttonRGetAllowBtn.Name = "buttonRGetAllowBtn";
            buttonRGetAllowBtn.Size = new Size(183, 29);
            buttonRGetAllowBtn.TabIndex = 31;
            buttonRGetAllowBtn.Text = "Record Get AllowBtn";
            buttonRGetAllowBtn.UseVisualStyleBackColor = true;
            buttonRGetAllowBtn.Click += buttonRGetAllowBtn_Click;
            // 
            // buttonRSetAllowBtn
            // 
            buttonRSetAllowBtn.Location = new Point(224, 468);
            buttonRSetAllowBtn.Margin = new Padding(4);
            buttonRSetAllowBtn.Name = "buttonRSetAllowBtn";
            buttonRSetAllowBtn.Size = new Size(183, 29);
            buttonRSetAllowBtn.TabIndex = 30;
            buttonRSetAllowBtn.Text = "Record Set AllowBtn";
            buttonRSetAllowBtn.UseVisualStyleBackColor = true;
            buttonRSetAllowBtn.Click += buttonRSetAllowBtn_Click;
            // 
            // buttonRGetMaxDur
            // 
            buttonRGetMaxDur.Location = new Point(224, 431);
            buttonRGetMaxDur.Margin = new Padding(4);
            buttonRGetMaxDur.Name = "buttonRGetMaxDur";
            buttonRGetMaxDur.Size = new Size(183, 29);
            buttonRGetMaxDur.TabIndex = 29;
            buttonRGetMaxDur.Text = "Record Get MaxDur";
            buttonRGetMaxDur.UseVisualStyleBackColor = true;
            buttonRGetMaxDur.Click += buttonRGetMaxDur_Click;
            // 
            // textBoxRMaxDur
            // 
            textBoxRMaxDur.Location = new Point(414, 400);
            textBoxRMaxDur.Name = "textBoxRMaxDur";
            textBoxRMaxDur.Size = new Size(57, 27);
            textBoxRMaxDur.TabIndex = 28;
            // 
            // labelRGetDur
            // 
            labelRGetDur.AutoSize = true;
            labelRGetDur.Location = new Point(420, 437);
            labelRGetDur.Margin = new Padding(4, 0, 4, 0);
            labelRGetDur.Name = "labelRGetDur";
            labelRGetDur.Size = new Size(51, 19);
            labelRGetDur.TabIndex = 27;
            labelRGetDur.Text = "label1";
            // 
            // buttonRSetMaxDur
            // 
            buttonRSetMaxDur.Location = new Point(224, 400);
            buttonRSetMaxDur.Margin = new Padding(4);
            buttonRSetMaxDur.Name = "buttonRSetMaxDur";
            buttonRSetMaxDur.Size = new Size(183, 29);
            buttonRSetMaxDur.TabIndex = 26;
            buttonRSetMaxDur.Text = "Record Set MaxDur";
            buttonRSetMaxDur.UseVisualStyleBackColor = true;
            buttonRSetMaxDur.Click += buttonRSetMaxDur_Click;
            // 
            // labelVolume
            // 
            labelVolume.AutoSize = true;
            labelVolume.Location = new Point(162, 224);
            labelVolume.Margin = new Padding(4, 0, 4, 0);
            labelVolume.Name = "labelVolume";
            labelVolume.Size = new Size(51, 19);
            labelVolume.TabIndex = 25;
            labelVolume.Text = "label1";
            // 
            // buttonPGetVolume
            // 
            buttonPGetVolume.Location = new Point(4, 224);
            buttonPGetVolume.Margin = new Padding(4);
            buttonPGetVolume.Name = "buttonPGetVolume";
            buttonPGetVolume.Size = new Size(153, 29);
            buttonPGetVolume.TabIndex = 24;
            buttonPGetVolume.Text = "Player Get Volume";
            buttonPGetVolume.UseVisualStyleBackColor = true;
            buttonPGetVolume.Click += buttonPGetVolume_Click;
            // 
            // labelPlayStatus
            // 
            labelPlayStatus.AutoSize = true;
            labelPlayStatus.Location = new Point(162, 82);
            labelPlayStatus.Margin = new Padding(4, 0, 4, 0);
            labelPlayStatus.Name = "labelPlayStatus";
            labelPlayStatus.Size = new Size(51, 19);
            labelPlayStatus.TabIndex = 23;
            labelPlayStatus.Text = "label1";
            // 
            // labelRecordStatus
            // 
            labelRecordStatus.AutoSize = true;
            labelRecordStatus.Location = new Point(162, 441);
            labelRecordStatus.Margin = new Padding(4, 0, 4, 0);
            labelRecordStatus.Name = "labelRecordStatus";
            labelRecordStatus.Size = new Size(51, 19);
            labelRecordStatus.TabIndex = 22;
            labelRecordStatus.Text = "label1";
            // 
            // labelRCnt
            // 
            labelRCnt.AutoSize = true;
            labelRCnt.Location = new Point(165, 405);
            labelRCnt.Margin = new Padding(4, 0, 4, 0);
            labelRCnt.Name = "labelRCnt";
            labelRCnt.Size = new Size(51, 19);
            labelRCnt.TabIndex = 21;
            labelRCnt.Text = "label1";
            // 
            // buttonRDownload
            // 
            buttonRDownload.Location = new Point(4, 510);
            buttonRDownload.Margin = new Padding(4);
            buttonRDownload.Name = "buttonRDownload";
            buttonRDownload.Size = new Size(153, 29);
            buttonRDownload.TabIndex = 20;
            buttonRDownload.Text = "Record Download";
            buttonRDownload.UseVisualStyleBackColor = true;
            buttonRDownload.Click += buttonRDownload_Click;
            // 
            // buttonRErase
            // 
            buttonRErase.Location = new Point(4, 474);
            buttonRErase.Margin = new Padding(4);
            buttonRErase.Name = "buttonRErase";
            buttonRErase.Size = new Size(153, 29);
            buttonRErase.TabIndex = 19;
            buttonRErase.Text = "Record Erase";
            buttonRErase.UseVisualStyleBackColor = true;
            buttonRErase.Click += buttonRErase_Click;
            // 
            // buttonPVolumeDown
            // 
            buttonPVolumeDown.Location = new Point(162, 258);
            buttonPVolumeDown.Margin = new Padding(4);
            buttonPVolumeDown.Name = "buttonPVolumeDown";
            buttonPVolumeDown.Size = new Size(153, 29);
            buttonPVolumeDown.TabIndex = 18;
            buttonPVolumeDown.Text = "Player Volume -";
            buttonPVolumeDown.UseVisualStyleBackColor = true;
            buttonPVolumeDown.Click += buttonPVolumeDown_Click;
            // 
            // buttonPVolumeUp
            // 
            buttonPVolumeUp.Location = new Point(4, 258);
            buttonPVolumeUp.Margin = new Padding(4);
            buttonPVolumeUp.Name = "buttonPVolumeUp";
            buttonPVolumeUp.Size = new Size(153, 29);
            buttonPVolumeUp.TabIndex = 17;
            buttonPVolumeUp.Text = "Player Volume +";
            buttonPVolumeUp.UseVisualStyleBackColor = true;
            buttonPVolumeUp.Click += buttonPVolumeUp_Click;
            // 
            // buttonPResume
            // 
            buttonPResume.Location = new Point(4, 151);
            buttonPResume.Margin = new Padding(4);
            buttonPResume.Name = "buttonPResume";
            buttonPResume.Size = new Size(153, 29);
            buttonPResume.TabIndex = 16;
            buttonPResume.Text = "Player Resume";
            buttonPResume.UseVisualStyleBackColor = true;
            buttonPResume.Click += buttonPResume_Click;
            // 
            // buttonPStop
            // 
            buttonPStop.Location = new Point(4, 187);
            buttonPStop.Margin = new Padding(4);
            buttonPStop.Name = "buttonPStop";
            buttonPStop.Size = new Size(153, 29);
            buttonPStop.TabIndex = 15;
            buttonPStop.Text = "Player Stop";
            buttonPStop.UseVisualStyleBackColor = true;
            buttonPStop.Click += buttonPStop_Click;
            // 
            // buttonPPause
            // 
            buttonPPause.Location = new Point(4, 114);
            buttonPPause.Margin = new Padding(4);
            buttonPPause.Name = "buttonPPause";
            buttonPPause.Size = new Size(153, 29);
            buttonPPause.TabIndex = 14;
            buttonPPause.Text = "Player Pause";
            buttonPPause.UseVisualStyleBackColor = true;
            buttonPPause.Click += buttonPPause_Click;
            // 
            // panel2
            // 
            panel2.Location = new Point(28, 301);
            panel2.Name = "panel2";
            panel2.Size = new Size(213, 70);
            panel2.TabIndex = 18;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(30, 259);
            label1.Name = "label1";
            label1.Size = new Size(51, 19);
            label1.TabIndex = 19;
            label1.Text = "label1";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(478, 478);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(67, 19);
            label2.TabIndex = 34;
            label2.Text = "(1 OR 0)";
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(9F, 19F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1029, 570);
            Controls.Add(label1);
            Controls.Add(panel2);
            Controls.Add(panel1);
            Controls.Add(listBox1);
            Margin = new Padding(4);
            Name = "Form1";
            Text = "Form1";
            FormClosed += Form1_FormClosed;
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button buttonPlayURL;
        private TextBox textBoxPlayURL;
        private ListBox listBox1;
        private Button buttonRStart;
        private Button buttonRStop;
        private Button buttonPRecord;
        private TextBox textBoxRecordNum;
        private Button buttonGetPlayStatus;
        private Button buttonGetRCount;
        private Button buttonGetRStatus;
        private Panel panel1;
        private Button buttonPResume;
        private Button buttonPStop;
        private Button buttonPPause;
        private Button buttonPVolumeDown;
        private Button buttonPVolumeUp;
        private Button buttonRErase;
        private Label labelPlayStatus;
        private Label labelRecordStatus;
        private Label labelRCnt;
        private Button buttonRDownload;
        private Button buttonPGetVolume;
        private Label labelVolume;
        private Panel panel2;
        private Label label1;
        private Button buttonRSetMaxDur;
        private TextBox textBoxRMaxDur;
        private Label labelRGetDur;
        private Button buttonRGetAllowBtn;
        private Button buttonRSetAllowBtn;
        private Button buttonRGetMaxDur;
        private TextBox textBoxRAllowBtn;
        private Label labelRGetAllownBtn;
        private Label label2;
    }
}