using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AFWifiServerDemo.entitys;
using NLog;

namespace AFWifiServerDemo.src
{
    /// <summary>
    /// 数据库服务类
    /// 封装SqlSugar的数据库操作，提供统一的数据访问接口
    /// </summary>
    public class DatabaseService : IDisposable
    {
        private readonly SqlSugarScope _db;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private static DatabaseService? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static DatabaseService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new DatabaseService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数，实现单例模式
        /// </summary>
        private DatabaseService()
        {
            var connectionString = DatabaseConfig.GetConnectionString();
            
            if (!DatabaseConfig.ValidateConnectionString(connectionString))
            {
                throw new InvalidOperationException("无效的数据库连接字符串");
            }

            _db = new SqlSugarScope(new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
                ConfigureExternalServices = new ConfigureExternalServices()
                {
                    EntityService = (c, p) =>
                    {
                        // 自动转换实体名称
                        if (p.DbColumnName.ToLower() == "id")
                        {
                            p.IsPrimarykey = true;
                        }
                    }
                }
            });

            // 配置SQL日志
            if (DatabaseConfig.EnableSqlLogging)
            {
                _db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    _logger.Debug($"SQL执行: {sql}");
                    if (pars != null && pars.Length > 0)
                    {
                        _logger.Debug($"参数: {string.Join(", ", pars.Select(p => $"{p.ParameterName}={p.Value}"))}");
                    }
                };

                _db.Aop.OnError = (exp) =>
                {
                    _logger.Error(exp, "数据库操作发生错误");
                };
            }

            _logger.Info($"数据库服务初始化完成: {DatabaseConfig.GetConfigInfo()}");
        }

        /// <summary>
        /// 获取SqlSugar数据库实例
        /// </summary>
        public SqlSugarScope GetDatabase()
        {
            return _db;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                //var result = await _db.Ado.GetDataTableAsync("SELECT 1");
                _logger.Info("数据库连接测试成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "数据库连接测试失败");
                return false;
            }
        }

        /// <summary>
        /// 创建数据库表（如果不存在）
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                // 创建表（如果不存在）
                _db.CodeFirst.InitTables(
                    typeof(MD_PaperMacMapping)
                    // typeof(Exam_Student),
                    // typeof(Exam_TeacherSubject),
                    // typeof(MD_UserPenMapping)
                );

                _logger.Info("数据库表初始化完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "数据库表初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        public async Task<Base_User?> GetUserAsync(string userId)
        {
            try
            {
                return await _db.Queryable<Base_User>()
                    .Where(u => u.Id == userId && !u.Deleted)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"获取用户信息失败，用户ID: {userId}");
                return null;
            }
        }

        /// <summary>
        /// 获取所有用户列表
        /// </summary>
        /// <param name="pageIndex">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>用户列表</returns>
        public async Task<(List<Base_User> users, int totalCount)> GetUsersAsync(int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                RefAsync<int> totalCount = 0;
                var users = await _db.Queryable<Base_User>()
                    .Where(u => !u.Deleted)
                    .OrderBy(u => u.CreateTime, OrderByType.Desc)
                    .ToPageListAsync(pageIndex, pageSize, totalCount);

                return (users, totalCount.Value);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "获取用户列表失败");
                return (new List<Base_User>(), 0);
            }
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> CreateUserAsync(Base_User user)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(user.Id))
                {
                    user.Id = Guid.NewGuid().ToString();
                }
                user.CreateTime = DateTime.Now;
                user.Deleted = false;

                var result = await _db.Insertable(user).ExecuteCommandAsync();
                _logger.Info($"创建用户成功，用户ID: {user.Id}");
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"创建用户失败，用户名: {user.UserName}");
                return false;
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateUserAsync(Base_User user)
        {
            try
            {
                var result = await _db.Updateable(user)
                    .Where(u => u.Id == user.Id)
                    .ExecuteCommandAsync();
                
                _logger.Info($"更新用户成功，用户ID: {user.Id}");
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"更新用户失败，用户ID: {user.Id}");
                return false;
            }
        }

        /// <summary>
        /// 删除用户（软删除）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteUserAsync(string userId)
        {
            try
            {
                var result = await _db.Updateable<Base_User>()
                    .SetColumns(u => u.Deleted == true)
                    .Where(u => u.Id == userId)
                    .ExecuteCommandAsync();

                _logger.Info($"删除用户成功，用户ID: {userId}");
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"删除用户失败，用户ID: {userId}");
                return false;
            }
        }

        /// <summary>
        /// 根据用户名查找用户
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <returns>用户信息</returns>
        public async Task<Base_User?> GetUserByNameAsync(string userName)
        {
            try
            {
                return await _db.Queryable<Base_User>()
                    .Where(u => u.UserName == userName && !u.Deleted)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"根据用户名查找用户失败，用户名: {userName}");
                return null;
            }
        }

        /// <summary>
        /// 执行原生SQL查询
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>查询结果</returns>
        public async Task<List<T>> QueryAsync<T>(string sql, object? parameters = null)
        {
            try
            {
                return await _db.Ado.SqlQueryAsync<T>(sql, parameters);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"执行SQL查询失败: {sql}");
                return new List<T>();
            }
        }

        /// <summary>
        /// 执行原生SQL命令
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <returns>影响的行数</returns>
        public async Task<int> ExecuteAsync(string sql, object? parameters = null)
        {
            try
            {
                return await _db.Ado.ExecuteCommandAsync(sql, parameters);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"执行SQL命令失败: {sql}");
                return 0;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _db?.Dispose();
            _logger.Info("数据库服务已释放");
        }
    }
}
