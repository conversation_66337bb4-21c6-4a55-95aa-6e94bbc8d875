﻿using AFPen;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AFWifiServerDemo
{
    public partial class Form1 : Form
    {
        int gVol = 10;
        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_FormClosed(object sender, FormClosedEventArgs e)
        {
            Environment.Exit(0);
        }

        private void buttonPlayURL_Click(object sender, EventArgs e)
        {
            //http://192.168.137.1:5000/api/MyApi/12.mp3
            PenCtrl.AFMediaPlayer.RequestPlayerPlay((String)listBox1.SelectedItem, textBoxPlayURL.Text);
        }

        public void onMediaPlayerCallback(string macstr, AFE_MediaPlayer_EVENT_TYPE t, int state, byte[] data)
        {
            this.Invoke(new MethodInvoker(delegate ()
            {

                if (data != null)
                {
                    Console.WriteLine($"{macstr} {t} {state} {data.Length}");
                    File.WriteAllBytes("test.amr", data);
                }
                else
                {
                    switch (t)
                    {
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderFileCnt:
                            PenCtrl.AFMediaPlayer.RequestRecorderDownloadFile((String)listBox1.SelectedItem, state);
                            labelRCnt.Text = String.Format($"{state}");
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderStart:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderStop:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderDownloadFile:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderErase:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderGetStatus:
                            labelRecordStatus.Text = String.Format($"{state}");
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderSetMaxDuration:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderGetMaxDuration:
                            labelRGetDur.Text = String.Format($"{state}");
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderSetAllowBtnRecord:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderGetAllowBtnRecord:
                            labelRGetAllownBtn.Text = String.Format($"{state}");
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerPlay:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerPause:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerResume:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerStop:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerChangeVolume:
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerGetStatus:
                            labelPlayStatus.Text = String.Format($"{state}");
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerGetVolume:
                            labelVolume.Text = String.Format($"{state}");
                            break;
                        case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerErr:
                            break;
                    }
                    Console.WriteLine($"{macstr} {t} {state}");
                }
            }));


        }

        public void updateList()
        {
            this.Invoke(new MethodInvoker(delegate ()
            {

                List<string> l = PenCtrl.Instance.AFAP_GetAllConnectedDevice();
                listBox1.BeginUpdate();
                listBox1.Items.Clear();
                for (int i = 0; i < l.Count; i++)
                {
                    listBox1.Items.Add(l[i]);
                }
                if (listBox1.SelectedItem == null && listBox1.Items.Count > 0)
                    listBox1.SelectedIndex = 0;
                listBox1.EndUpdate();
                listBox1.Invalidate();

                label1.Text = $"{listBox1.Items.Count}";
            }));
        }

        private void buttonRStart_Click(object sender, EventArgs e)
        {
            PenCtrl.AFMediaPlayer.RequestRecorderStart((String)listBox1.SelectedItem);
        }

        private void buttonPRecord_Click(object sender, EventArgs e)
        {
            PenCtrl.AFMediaPlayer.RequestPlayerPlay((String)listBox1.SelectedItem, Convert.ToInt32(textBoxRecordNum.Text));
        }

        private void buttonGetPlayStatus_Click(object sender, EventArgs e)
        {
            labelPlayStatus.Text = "";
            PenCtrl.AFMediaPlayer.RequestPlayerStatus((String)listBox1.SelectedItem);
        }

        private void buttonGetRCount_Click(object sender, EventArgs e)
        {
            labelRCnt.Text = "";
            PenCtrl.AFMediaPlayer.RequestRecorderGetCnt((String)listBox1.SelectedItem);
        }

        private void buttonGetRStatus_Click(object sender, EventArgs e)
        {
            labelRecordStatus.Text = "";
            PenCtrl.AFMediaPlayer.RequestRecorderStatus((String)listBox1.SelectedItem);
        }

        private void buttonPPause_Click(object sender, EventArgs e)
        {
            PenCtrl.AFMediaPlayer.RequestPlayerPause((String)listBox1.SelectedItem);
        }

        private void buttonPResume_Click(object sender, EventArgs e)
        {

            PenCtrl.AFMediaPlayer.RequestPlayerResume((String)listBox1.SelectedItem);
        }

        private void buttonPStop_Click(object sender, EventArgs e)
        {

            PenCtrl.AFMediaPlayer.RequestPlayerStop((String)listBox1.SelectedItem);
        }

        private void buttonPVolumeUp_Click(object sender, EventArgs e)
        {
            gVol = (gVol + 1) > 10 ? 10 : (gVol + 1);
            PenCtrl.AFMediaPlayer.RequestPlayerChangeVolume((String)listBox1.SelectedItem, gVol);
        }

        private void buttonPVolumeDown_Click(object sender, EventArgs e)
        {
            gVol = (gVol - 1) > 0 ? (gVol - 1) : 1;
            PenCtrl.AFMediaPlayer.RequestPlayerChangeVolume((String)listBox1.SelectedItem, gVol);
        }

        private void buttonRErase_Click(object sender, EventArgs e)
        {

            PenCtrl.AFMediaPlayer.RequestRecorderErase((String)listBox1.SelectedItem);
        }

        private void buttonRDownload_Click(object sender, EventArgs e)
        {
            PenCtrl.AFMediaPlayer.RequestRecorderDownloadFile((String)listBox1.SelectedItem, Convert.ToInt32(textBoxRecordNum.Text));
        }

        private void buttonPGetVolume_Click(object sender, EventArgs e)
        {
            labelVolume.Text = "";
            PenCtrl.AFMediaPlayer.RequestPlayerGetVolume((String)listBox1.SelectedItem);
        }

        private void buttonRStop_Click(object sender, EventArgs e)
        {

            PenCtrl.AFMediaPlayer.RequestRecorderStop((String)listBox1.SelectedItem);
        }

        private void listBox1_SizeChanged(object sender, EventArgs e)
        {
            label1.Text = $"{listBox1.Items.Count}";
        }

        private void buttonRSetMaxDur_Click(object sender, EventArgs e)
        {
            int ret = PenCtrl.AFMediaPlayer.RequestRecorderSetRecordMaxDuration((String)listBox1.SelectedItem, Convert.ToInt32(textBoxRMaxDur.Text));
            ConsoleOutput("RequestRecorderSetRecordMaxDuration ret=" + ret);
        }

        private void buttonRGetMaxDur_Click(object sender, EventArgs e)
        {
            int ret = PenCtrl.AFMediaPlayer.RequestRecorderGetRecordMaxDuration((String)listBox1.SelectedItem);
            ConsoleOutput("RequestRecorderGetRecordMaxDuration ret=" + ret);
        }

        private void buttonRSetAllowBtn_Click(object sender, EventArgs e)
        {
            int ret = PenCtrl.AFMediaPlayer.RequestRecorderSetAllowBtnRecord((String)listBox1.SelectedItem, Convert.ToInt32(textBoxRAllowBtn.Text));
            ConsoleOutput("RequestRecorderSetAllowBtnRecord ret=" + ret);
        }

        private void buttonRGetAllowBtn_Click(object sender, EventArgs e)
        {
            int ret = PenCtrl.AFMediaPlayer.RequestRecorderGetAllowBtnRecord((String)listBox1.SelectedItem);
            ConsoleOutput("RequestRecorderGetAllowBtnRecord ret=" + ret);
        }

        public void ConsoleOutput(string s)
        {
            Console.WriteLine(s);
        }
    }
}
