{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AFWifiServerDemo/1.0.0": {"dependencies": {"NLog": "5.3.2", "LiteDB": "5.0.21.0", "NetCoreServer": "8.0.7.0", "xb": "1.0.0.0"}, "runtime": {"AFWifiServerDemo.dll": {}}}, "NLog/5.3.2": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.3.2.2526"}}}, "LiteDB/5.0.21.0": {"runtime": {"LiteDB.dll": {"assemblyVersion": "5.0.21.0", "fileVersion": "5.0.21.0"}}}, "NetCoreServer/8.0.7.0": {"runtime": {"NetCoreServer.dll": {"assemblyVersion": "8.0.7.0", "fileVersion": "8.0.7.0"}}}, "xb/1.0.0.0": {"runtime": {"xb.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "System.Data.SQLite/1.0.119.0": {"runtime": {"System.Data.SQLite.dll": {"assemblyVersion": "1.0.119.0", "fileVersion": "1.0.119.0"}}}}}, "libraries": {"AFWifiServerDemo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "NLog/5.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-cQR<PERSON>ZuDMjSyMe9EQBnI9v55YAMMz8GAfFd6fFJ6tc/kXnG7Hze8p1I8MgvWSBG6E36wA8vSxRrlm8uSIG+SENg==", "path": "nlog/5.3.2", "hashPath": "nlog.5.3.2.nupkg.sha512"}, "LiteDB/5.0.21.0": {"type": "reference", "serviceable": false, "sha512": ""}, "NetCoreServer/8.0.7.0": {"type": "reference", "serviceable": false, "sha512": ""}, "xb/1.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Data.SQLite/1.0.119.0": {"type": "reference", "serviceable": false, "sha512": ""}}}