﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PlatformTarget>x64</PlatformTarget>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <UseWindowsForms>True</UseWindowsForms>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="LiteDB">
      <HintPath>..\..\..\..\AFPenDemoWinSrc\AFPenSDKWinMulti\Libs\LiteDB.dll</HintPath>
    </Reference>
    <Reference Include="NetCoreServer">
      <HintPath>deps\NetCoreServer.dll</HintPath>
    </Reference>
    <Reference Include="xb">
      <HintPath>Libs\xb.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="LiteDB.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="msvcp140.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="msvcp140_1.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="msvcp140_2.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="System.Data.SQLite.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="vcruntime140.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="vcruntime140_1.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="xa.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="deps\" />
    <Folder Include="Libs\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.1.1" />
    <PackageReference Include="NLog" Version="5.3.2" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.199" />
  </ItemGroup>

  <Target Name="CopyDependencies" AfterTargets="Build">
    <ItemGroup>
        <DependencyFiles Include="$(ProjectDir)deps\**\*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(DependencyFiles)" DestinationFolder="$(OutputPath)\%(RecursiveDir)" />
</Target>
</Project>
