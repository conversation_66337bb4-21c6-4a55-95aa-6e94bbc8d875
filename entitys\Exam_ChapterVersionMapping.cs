using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SqlSugar;

namespace AFWifiServerDemo.entitys
{
 public class Exam_ChapterVersionMapping
{
	[SugarColumn(IsPrimaryKey =true)]
	public string Id { get; set; }

	public string ChapterId { get; set; }


	public string ChapterName { get; set; }

	/// <summary>
	/// 版本，也就是学年
	/// </summary>
	public int Version { get; set; }


	/// <summary>
	/// 否已删除
	/// </summary>
	public Boolean Deleted { get; set; }

	/// <summary>
	/// 创建时间
	/// </summary>
	public DateTime? CreateTime { get; set; }
}
}