﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AFWifiServerDemo.entitys
{
    /// <summary>
    /// 学生,教师笔绑定映射
    /// </summary>
    [Table("MD_UserPenMapping")]
    public class MD_UserPenMapping
    {
        [Key]
        public string Id { get; set; }
        /// <summary>
        /// 学生Id
        /// </summary>
        public string UserId { get; set; }
        /// <summary>
        /// 笔Mac
        /// </summary>
        public string PMac { get; set; }

        /// <summary>
        /// 是否删除 0 不删 1 删除
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime Createtime { get; set; }

        /// <summary>
        /// 连接状态 0 未连接  1 已连接
        /// </summary>
        public int state { get; set; }

        /// <summary>
        /// 用户类型 0 学生 1 教师
        /// </summary>
        public int? UserType { get; set; }

        /// <summary>
        /// 电量
        /// </summary>
        public int Battery { get; set; }

        /// <summary>
        /// 配网WIFI名称
        /// </summary>
        public string Ssid { get; set; } = "";

        /// <summary>
        /// 固件版本
        /// </summary>
        public string Version { get; set; } = "";
    }
}
