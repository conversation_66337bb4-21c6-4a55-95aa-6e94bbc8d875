-- 章节版本映射数据复制功能测试SQL

-- 1. 查看原始数据（测试用的SQL语句）
SELECT 
    cvm.Id,
    cvm.ChapterId,
    cvm.ChapterName,
    cvm.Version,
    cvm.Deleted,
    cvm.CreateTime,
    sc.WeekId
FROM Exam_ChapterVersionMapping cvm
JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
WHERE cvm.Version = 2024 
  AND sc.WeekId = 1 
  AND cvm.Deleted = 0
ORDER BY cvm.CreateTime;

-- 2. 查看复制后的数据（假设复制到2025版本）
SELECT 
    cvm.Id,
    cvm.ChapterId,
    cvm.ChapterName,
    cvm.Version,
    cvm.Deleted,
    cvm.CreateTime,
    sc.WeekId
FROM Exam_ChapterVersionMapping cvm
JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
WHERE cvm.Version = 2025 
  AND sc.WeekId = 1 
  AND cvm.Deleted = 0
ORDER BY cvm.CreateTime;

-- 3. 对比复制前后的数据数量
SELECT 
    '复制前(2024版本)' AS 数据类型,
    COUNT(*) AS 记录数量
FROM Exam_ChapterVersionMapping cvm
JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
WHERE cvm.Version = 2024 
  AND sc.WeekId = 1 
  AND cvm.Deleted = 0

UNION ALL

SELECT 
    '复制后(2025版本)' AS 数据类型,
    COUNT(*) AS 记录数量
FROM Exam_ChapterVersionMapping cvm
JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
WHERE cvm.Version = 2025 
  AND sc.WeekId = 1 
  AND cvm.Deleted = 0;

-- 4. 验证ID和CreateTime是否已更新
SELECT 
    '原始数据' AS 数据来源,
    cvm.Id,
    cvm.ChapterId,
    cvm.ChapterName,
    cvm.Version,
    cvm.CreateTime
FROM Exam_ChapterVersionMapping cvm
JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
WHERE cvm.Version = 2024 
  AND sc.WeekId = 1 
  AND cvm.Deleted = 0

UNION ALL

SELECT 
    '复制数据' AS 数据来源,
    cvm.Id,
    cvm.ChapterId,
    cvm.ChapterName,
    cvm.Version,
    cvm.CreateTime
FROM Exam_ChapterVersionMapping cvm
JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
WHERE cvm.Version = 2025 
  AND sc.WeekId = 1 
  AND cvm.Deleted = 0
ORDER BY 数据来源, ChapterId;

-- 5. 清理测试数据（可选，谨慎使用）
-- DELETE FROM Exam_ChapterVersionMapping 
-- WHERE Version = 2025 
--   AND Id IN (
--       SELECT cvm.Id 
--       FROM Exam_ChapterVersionMapping cvm
--       JOIN Exam_SubjectChapter sc ON sc.Id = cvm.ChapterId
--       WHERE cvm.Version = 2025 
--         AND sc.WeekId = 1
--   );
