is_global = true
build_property.ApplicationManifest = 
build_property.StartupObject = 
build_property.ApplicationDefaultFont = 
build_property.ApplicationHighDpiMode = 
build_property.ApplicationUseCompatibleTextRendering = 
build_property.ApplicationVisualStyles = 
build_property.TargetFramework = net8.0-windows8.0
build_property.TargetPlatformMinVersion = 8.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AFWifiServerDemo
build_property.ProjectDir = G:\project\csharp\AFWifiServerSDKDemo_X64_241216\AFWifiServerSDKDemo\AFWifiServerDemo\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
