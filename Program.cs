using AFPen;
using AFPen.Public;
using AFPen.Public.EvtStruct;
using AFPen.Public.ActStructure;
using System.Runtime.InteropServices;
using xb;
using AFWifiServerDemo;
using NLog.Config;
using NLog.Targets;
using NLog;
using System.Threading;
using System.Security.Cryptography;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using AFWifiServerDemo.models;
using AFWifiServerDemo.src;
using AFWifiServerDemo.entitys;

// 主程序入口
await MainAsync();

/// <summary>
/// 主程序异步方法
/// </summary>
static async Task MainAsync()
{
    Dictionary<ulong, PenPrensenter> pens = new Dictionary<ulong, PenPrensenter>();
    AFPenEvent mAF_IPenEvent = new AFPenEvent(pens);

    // 初始化数据库服务
    await InitializeDatabaseAsync();

    mAF_IPenEvent.start();

    Console.WriteLine("程序已启动，数据库连接已建立。");
    Console.WriteLine("可用的键盘命令：");
    Console.WriteLine("1 - 获取电池信息");
    Console.WriteLine("2 - 获取固件版本");
    Console.WriteLine("Z - 同步所有离线数据");
    Console.WriteLine("X - 获取所有连接的设备");
    Console.WriteLine("= - 删除当前连接笔数据");
    Console.WriteLine("V - 启动服务器");
    Console.WriteLine("B - 停止服务器");
    Console.WriteLine("H - 更新固件");
    Console.WriteLine("K - 获取待机时间");
    Console.WriteLine("L - 设置关机时间");
    Console.WriteLine("O - 设置USB重连时间");
    Console.WriteLine("P - 获取USB重连时间");
    Console.WriteLine("--- 数据库操作命令 ---");
    Console.WriteLine("U - 创建测试用户");
    Console.WriteLine("G - 获取用户列表");
    Console.WriteLine("T - 测试数据库连接");
    Console.WriteLine("D - 删除用户（软删除）");
    Console.WriteLine("F - 根据用户名查找用户");
    Console.WriteLine("M - 创建笔用户映射");
    Console.WriteLine("S - 执行自定义SQL查询");
    Console.WriteLine(". - 退出程序");

    /*press 1 to test fetch battery*/
    while (true)
    {
        ConsoleKeyInfo info = Console.ReadKey();
        if (pens.Count > 0)
        {
            String strmac = Util.LongMac2String(pens.First().Key);
            if (info.KeyChar == '1')
            {
                PenCtrl.Instance.AFAP_GetBattery(strmac);
            }
            else if (info.KeyChar == '2')
            {
                //PenCtrl.Instance.requestUpdateFW(strmac, bytes);
                PenCtrl.Instance.AFAP_GetFirmwareVer(strmac);
            }
            else if (info.KeyChar == 'Z') //sync all offline
            {
                if (PenCtrl.Instance.AFAP_GetOfflineAvailableCnt(strmac) > 0)
                    PenCtrl.Instance.AFAP_RequestAllOfflineData(strmac);
            }
            else if (info.KeyChar == 'X') // get all connected
            {
                PenCtrl.Instance.AFAP_GetAllConnectedDevice().ForEach(s =>
                {
                    Console.WriteLine(s);
                });

            }
            else if (info.KeyChar == '=') //delete current connect pendata
            {
                PenCtrl.Instance.AFAP_DeleteOfflineData(strmac);

            }
            else if (info.KeyChar == 'V') //start server
            {
                PenCtrl.Instance.AFAP_StartRouting();

            }
            else if (info.KeyChar == 'B') //stop server
            {
                PenCtrl.Instance.AFAP_StopRouting();

            }
            else if (info.KeyChar == 'H')
            {

                byte[] bytes = File.ReadAllBytes("OTA_All_new61wps250710.bin");

                PenCtrl.Instance.requestUpdateFW(strmac, bytes);
            }
            else if (info.KeyChar == 'K')
            {
                //AFAP_RequestGetStandbyTime
                PenCtrl.Instance.AFAP_RequestGetStandbyTime(strmac);
            }
            else if (info.KeyChar == 'L')
            {
                //AFAP_RequestSetPowerDownTime
                PenCtrl.Instance.AFAP_RequestSetPowerDownTime(strmac, 10);
            }
            else if (info.KeyChar == 'O')
            {
                PenCtrl.Instance.AFAP_SetPenUSBReconnectMinute(strmac, 3);
            }
            else if (info.KeyChar == 'P')
            {
                //AFAP_RequestSetPowerDownTime
                PenCtrl.Instance.AFAP_GetPenUSBReconnectMinute(strmac);
            }
        }

        // 数据库操作命令
        if (info.KeyChar == 'U') // 创建测试用户
        {
            await CreateTestUserAsync();
        }
        else if (info.KeyChar == 'G') // 获取用户列表
        {
            await GetUserListAsync();
        }
        else if (info.KeyChar == 'T') // 测试数据库连接
        {
            await TestDatabaseConnectionAsync();
        }
        else if (info.KeyChar == 'D') // 删除用户
        {
            await DeleteUserAsync();
        }
        else if (info.KeyChar == 'F') // 根据用户名查找用户
        {
            await FindUserByNameAsync();
        }
        else if (info.KeyChar == 'M') // 创建笔用户映射
        {
            await CreatePenMappingAsync();
        }
        else if (info.KeyChar == 'S') // 执行自定义SQL查询
        {
            await ExecuteCustomSqlAsync();
        }

        if (info.KeyChar == '.')
        {
            break;
        }
    }
}

/// <summary>
/// 初始化数据库服务
/// </summary>
static async Task InitializeDatabaseAsync()
{
    try
    {
        Console.WriteLine("正在初始化数据库服务...");
        Console.WriteLine(DatabaseConfig.GetConfigInfo());

        // 测试数据库连接
        bool connectionSuccess = await DatabaseService.Instance.TestConnectionAsync();
        if (!connectionSuccess)
        {
            Console.WriteLine("数据库连接失败！请检查连接字符串和网络连接。");
            return;
        }

        // 初始化数据库表
        bool initSuccess = await DatabaseService.Instance.InitializeDatabaseAsync();
        if (initSuccess)
        {
            Console.WriteLine("数据库初始化成功！");
        }
        else
        {
            Console.WriteLine("数据库表初始化失败！");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"数据库初始化异常: {ex.Message}");
    }
}

/// <summary>
/// 创建测试用户
/// </summary>
static async Task CreateTestUserAsync()
{
    try
    {
        Console.WriteLine("正在创建测试用户...");
        
        var testUser = new Base_User
        {
            Id = Guid.NewGuid().ToString(),
            UserName = $"testuser_{DateTime.Now:yyyyMMddHHmmss}",
            RealName = "测试用户",
            NickName = "测试昵称",
            Password = "123456",
            Sex = 1,
            CreateTime = DateTime.Now,
            Deleted = false,
            PhoneNum = "13800138000",
            SchoolId = "test_school_001"
        };

        bool success = await DatabaseService.Instance.CreateUserAsync(testUser);
        if (success)
        {
            Console.WriteLine($"测试用户创建成功！用户ID: {testUser.Id}, 用户名: {testUser.UserName}");
        }
        else
        {
            Console.WriteLine("测试用户创建失败！");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"创建测试用户异常: {ex.Message}");
    }
}

/// <summary>
/// 获取用户列表
/// </summary>
static async Task GetUserListAsync()
{
    try
    {
        Console.WriteLine("正在获取用户列表...");
        
        var (users, totalCount) = await DatabaseService.Instance.GetUsersAsync(1, 10);
        
        Console.WriteLine($"用户总数: {totalCount}");
        Console.WriteLine("用户列表:");
        Console.WriteLine("ID\t\t\t\t\t用户名\t\t真实姓名\t创建时间");
        Console.WriteLine(new string('-', 80));
        
        foreach (var user in users)
        {
            Console.WriteLine($"{user.Id}\t{user.UserName}\t\t{user.RealName}\t{user.CreateTime:yyyy-MM-dd HH:mm:ss}");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"获取用户列表异常: {ex.Message}");
    }
}

/// <summary>
/// 测试数据库连接
/// </summary>
static async Task TestDatabaseConnectionAsync()
{
    try
    {
        Console.WriteLine("正在测试数据库连接...");
        
        bool success = await DatabaseService.Instance.TestConnectionAsync();
        if (success)
        {
            Console.WriteLine("数据库连接测试成功！");
        }
        else
        {
            Console.WriteLine("数据库连接测试失败！");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"数据库连接测试异常: {ex.Message}");
    }
}

/// <summary>
/// 删除用户（软删除）
/// </summary>
static async Task DeleteUserAsync()
{
    try
    {
        Console.Write("请输入要删除的用户ID: ");
        string? userId = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(userId))
        {
            Console.WriteLine("用户ID不能为空！");
            return;
        }

        // 先查询用户是否存在
        var user = await DatabaseService.Instance.GetUserAsync(userId);
        if (user == null)
        {
            Console.WriteLine("用户不存在！");
            return;
        }

        Console.WriteLine($"找到用户: {user.UserName} ({user.RealName})");
        Console.Write("确认删除？(y/n): ");
        string? confirm = Console.ReadLine();

        if (confirm?.ToLower() == "y")
        {
            bool success = await DatabaseService.Instance.DeleteUserAsync(userId);
            if (success)
            {
                Console.WriteLine("用户删除成功！");
            }
            else
            {
                Console.WriteLine("用户删除失败！");
            }
        }
        else
        {
            Console.WriteLine("取消删除操作。");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"删除用户异常: {ex.Message}");
    }
}

/// <summary>
/// 根据用户名查找用户
/// </summary>
static async Task FindUserByNameAsync()
{
    try
    {
        Console.Write("请输入用户名: ");
        string? userName = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(userName))
        {
            Console.WriteLine("用户名不能为空！");
            return;
        }

        var user = await DatabaseService.Instance.GetUserByNameAsync(userName);
        if (user != null)
        {
            Console.WriteLine("找到用户:");
            Console.WriteLine($"ID: {user.Id}");
            Console.WriteLine($"用户名: {user.UserName}");
            Console.WriteLine($"真实姓名: {user.RealName}");
            Console.WriteLine($"昵称: {user.NickName}");
            Console.WriteLine($"手机号: {user.PhoneNum}");
            Console.WriteLine($"创建时间: {user.CreateTime:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"学校ID: {user.SchoolId}");
        }
        else
        {
            Console.WriteLine("未找到该用户！");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"查找用户异常: {ex.Message}");
    }
}

/// <summary>
/// 创建笔用户映射
/// </summary>
static async Task CreatePenMappingAsync()
{
    try
    {
        Console.Write("请输入用户ID: ");
        string? userId = Console.ReadLine();

        Console.Write("请输入笔MAC地址: ");
        string? penMac = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(userId) || string.IsNullOrWhiteSpace(penMac))
        {
            Console.WriteLine("用户ID和笔MAC地址不能为空！");
            return;
        }

        var mapping = new MD_UserPenMapping
        {
            Id = Guid.NewGuid().ToString(),
            UserId = userId,
            PMac = penMac,
            Deleted = 0,
            Createtime = DateTime.Now,
            state = 0, // 未连接
            UserType = 0, // 学生
            Battery = 0,
            Ssid = "",
            Version = ""
        };

        var db = DatabaseService.Instance.GetDatabase();
        var result = await db.Insertable(mapping).ExecuteCommandAsync();

        if (result > 0)
        {
            Console.WriteLine($"笔用户映射创建成功！映射ID: {mapping.Id}");
        }
        else
        {
            Console.WriteLine("笔用户映射创建失败！");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"创建笔用户映射异常: {ex.Message}");
    }
}

/// <summary>
/// 执行自定义SQL查询
/// </summary>
static async Task ExecuteCustomSqlAsync()
{
    try
    {
        Console.WriteLine("请选择操作类型:");
        Console.WriteLine("1 - 查询操作 (SELECT)");
        Console.WriteLine("2 - 更新操作 (UPDATE/INSERT/DELETE)");
        Console.Write("请输入选择 (1 或 2): ");

        string? choice = Console.ReadLine();

        Console.Write("请输入SQL语句: ");
        string? sql = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(sql))
        {
            Console.WriteLine("SQL语句不能为空！");
            return;
        }

        if (choice == "1")
        {
            // 查询操作
            var db = DatabaseService.Instance.GetDatabase();
            var result = await db.Ado.GetDataTableAsync(sql);

            Console.WriteLine($"查询结果 (共 {result.Rows.Count} 行):");

            // 显示列名
            for (int i = 0; i < result.Columns.Count; i++)
            {
                Console.Write($"{result.Columns[i].ColumnName}\t");
            }
            Console.WriteLine();
            Console.WriteLine(new string('-', result.Columns.Count * 15));

            // 显示数据
            foreach (System.Data.DataRow row in result.Rows)
            {
                for (int i = 0; i < result.Columns.Count; i++)
                {
                    Console.Write($"{row[i]}\t");
                }
                Console.WriteLine();
            }
        }
        else if (choice == "2")
        {
            // 更新操作
            int affectedRows = await DatabaseService.Instance.ExecuteAsync(sql);
            Console.WriteLine($"操作完成，影响行数: {affectedRows}");
        }
        else
        {
            Console.WriteLine("无效的选择！");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"执行SQL异常: {ex.Message}");
    }
}

class PenPrensenter
{

    public TaskCompletionSource<string> signalFw;
    public TaskCompletionSource<string> signalFwUpdateResult;
    public int cnt = 0;
    string fwStr = "";
    string strmac;
    private static int isSignalSet = 0;
    Task taskUpdateFW;

    public PenPrensenter(string strmac)
    {
        this.strmac = strmac;
    }

    public string FwStr {
        get => fwStr;
        set {
            fwStr = value;
            if (Interlocked.Exchange(ref isSignalSet, 1) == 0)
            {
                Interlocked.Exchange(ref isSignalSet, 1);
                signalFw.SetResult(fwStr);
            }
        }
    }

    public void updateFWTask()
    {
        if (taskUpdateFW == null)
        {
            taskUpdateFW = Task.Run(async () =>
            {
                signalFw = new TaskCompletionSource<string>();
                signalFwUpdateResult = new TaskCompletionSource<string>();
                PenCtrl.Instance.AFAP_GetFirmwareVer(strmac);
                string fwStr = await signalFw.Task;
                Console.WriteLine($"fw={fwStr}");
                if (fwStr.Contains("61WPS"))
                {

                    Console.WriteLine($"FwStr={FwStr}");
                    if(!fwStr.Equals("62RC 2024-12-19 14:38:59 AN 61WPS-O-US"))
                    {
                        byte[] bytes = File.ReadAllBytes("61wps_62RC_20241219N_UsbSync_OTA.bin");
                        PenCtrl.Instance.requestUpdateFW(strmac, bytes);
                        await signalFwUpdateResult.Task;
                        Console.WriteLine("signalFwUpdateResult done");
                    }
                }
            });
        }
    }
    public void onAFPenUpdaterReport(ulong id, int status, int progress)
    {
        AFUpdaterReport status0 = (AFUpdaterReport)status;
        String showtxt = "";
        switch (status0)
        {
            case AFUpdaterReport.WriteOTAReport:
                showtxt = status0 + " " + progress;
                Console.WriteLine(id + " " + status + " " + progress);
                break;
            case AFUpdaterReport.WriteOTADone:
                showtxt = "write done";
                Console.WriteLine(id + " " + showtxt);
                break;
            case AFUpdaterReport.FWWriteReport:
                showtxt = status0 + " " + progress;
                Console.WriteLine(id + " " + showtxt);
                if (progress == 100)
                    signalFwUpdateResult.SetResult("done");
                break;
            default:
                showtxt = "code=" + status0.ToString();
                Console.WriteLine(id + " " + showtxt);
                break;
        }
        //Console.WriteLine(showtxt);
    }
    public void OnReceivedPortMessage(string id, AFPortMsgType msgType, string jsonString)
    {
        ulong idulong = Util.StringMac2Long(id);
        switch (msgType)
        {
            case AFPortMsgType.PEN_CONNECTION_SUCCESS:
                Interlocked.Exchange(ref isSignalSet, 0);
                Console.WriteLine("AFPortMsgType.PEN_CONNECTION_SUCCESS " + id);
                break;
            case AFPortMsgType.PEN_DISCONNECTED:
                try
                {
                    if (taskUpdateFW != null)
                        taskUpdateFW.Dispose();
                }
                catch(Exception e) { }
                finally { taskUpdateFW = null;  }


                Console.WriteLine("AFPortMsgType.PEN_DISCONNECTED " + id);
                break;

        }
    }
}

class AFPenEvent : AF_IPenEvent, AF_IPortMsg, AF_IAFPenUpdaterReport, AF_IAFPenMediaPlayer
{
    //StreamWriter sw = new StreamWriter("a.txt");
    Dictionary<ulong, PenPrensenter> pens = new Dictionary<ulong, PenPrensenter>();
    Form1 form = new Form1();
    void initFormThread()
    {
        Thread t = new Thread(new ThreadStart(StartNewStaThrea));
        t.Start();
    }


    void StartNewStaThrea()
    {
        Application.Run(form);
    }
    public AFPenEvent(Dictionary<ulong, PenPrensenter> pens)
    {
        this.pens = pens;
    }

    public void start()
    {
        initFormThread();
        DemoServerUtil.Util.registerExitCallback();
        PenCtrl.Instance.AFAP_Init(8080);
        //開啟/關閉 檔案&Console輸出
        PenCtrl.Instance.SetConsoleLogEnabled(true);
        PenCtrl.Instance.AF_SetDejitterEnable(true);
        PenCtrl.Instance.AF_SetPortMsgListener(this);
        PenCtrl.Instance.AF_SetPenEventListener(this);
        //更新FW的回調介面
        PenCtrl.Instance.AFAP_SetUpdaterListener(this);
        //錄音筆支援
        PenCtrl.Instance.AFAP_SetMediaPlayerListener(this);
        //連線後自動同步
        PenCtrl.Instance.SetSyncAfterConnectEnabled(true);
        Console.WriteLine("");
        AFPaperSize a4p = new AFPaperSize(1, 1000000, 4961, 7040, 1);
        List<AFPaperSize> l = new List<AFPaperSize>();
        l.Add(a4p);
        PenCtrl.Instance.AF_SetPaperSize(l);

        //set nlogger style
        if (LogManager.Configuration == null || LogManager.Configuration.LoggingRules.Count == 0)
        {
            var config = new LoggingConfiguration();

            var logfile = new FileTarget("logfile")
            {
                FileName = "C:\\Logs\\mylogfile.txt", // 绝对路径
                                                      // FileName = "${basedir}/Logs/mylogfile_${shortdate}.txt",
                Layout = "${longdate} ${level:uppercase=true} ${message} ${exception:format=toString,StackTrace}"
            };

            var logconsole = new ConsoleTarget("logconsole")
            {
                Layout = "${longdate} ${level:uppercase=true} ${message} ${exception:format=toString,StackTrace}"
            };

            config.AddTarget(logfile);
            config.AddTarget(logconsole);

            var rule1 = new LoggingRule("LoggerAFSDK", LogLevel.Trace, logfile);
            var rule2 = new LoggingRule("LoggerAFSDK", LogLevel.Warn, logconsole);

            config.LoggingRules.Add(rule1);
            config.LoggingRules.Add(rule2);

            LogManager.Configuration = config;
        }
        //改成只顯示上下線資料
       // foreach (var item in LogManager.Configuration.LoggingRules.ToList())
       //     item.SetLoggingLevels(LogLevel.Info, LogLevel.Info);
        PenCtrl.Instance.setNLogger(LogManager.GetLogger("LoggerAFSDK"));
    }
    public void OnReceivedPortMessage(string id, AFPortMsgType msgType, string jsonString)
    {
        ulong idulong = Util.StringMac2Long(id);
        switch (msgType)
        {
            case AFPortMsgType.PEN_CONNECTION_SUCCESS:
                if (!pens.ContainsKey(Util.StringMac2Long(id)))
                    pens.Add(Util.StringMac2Long(id), new PenPrensenter(id));
            break;
            case AFPortMsgType.PEN_DISCONNECTED:
                pens[Util.StringMac2Long(id)].cnt = 0;
                break;

        }
        if (pens.ContainsKey(idulong))
            pens[idulong].OnReceivedPortMessage(id, msgType, jsonString);

            form.updateList();
    }

    /// <summary>
    /// 接收笔的事件回調
    /// </summary>
    /// <param name="id"></param>
    /// <param name="eventType"></param>
    /// <param name="eventObj"></param>
    public void OnReceivedMessage(string id, AFE_EVENT_TYPE eventType, object eventObj)
    {
        ulong idulong = Util.StringMac2Long(id);

        var xdata = new MessageData
        {
            Id = id,
            EventType = (int)eventType,
            EventData = eventObj
        };
        var xid = xdata.Key;
        var logid = $" id: {xid} , mac: {xdata.Mac} , type: {eventType} ";
      
        Console.WriteLine(logid);

        switch (eventType)
        {
            case AFE_EVENT_TYPE.AFE_OfflineTaskReady:
                {
                    //if (pens.ContainsKey(idulong))
                    //    pens[idulong].updateFWTask();
                }
                break;
            case AFE_EVENT_TYPE.AFE_Dot: //获取实时点位
                {
                    AFEDot_ dot = (AFEDot_)eventObj;

                    String text = String.Format("{5} X={0} Y={1} Page={2} t={3} pr={4}", dot.X, dot.Y, dot.page, dot.type, dot.pr, id);
                    Console.WriteLine(text);
                }
                break;
            case AFE_EVENT_TYPE.AFE_GetWifiSSID:
                {
                    Console.WriteLine("AFE_EVENT_TYPE.AFE_GetWifiSSID");
                    AFEGetWifiSSID_ objstruct = ((AFEGetWifiSSID_)eventObj);
                    byte[] data = new byte[objstruct.count];
                    unsafe { Marshal.Copy((IntPtr)objstruct.content, data, 0, data.Length); }
                    string sid = System.Text.Encoding.ASCII.GetString(data);
                    Console.WriteLine(sid);
                }
                break;
            case AFE_EVENT_TYPE.AFE_GetVersion:
                {
                    Console.WriteLine("AFE_EVENT_TYPE.AFE_GetVersion");
                    AFEGetVersion_ obj1 = (AFEGetVersion_)eventObj;
                    byte[] data = new byte[obj1.count];
                    unsafe { Marshal.Copy((IntPtr)obj1.version, data, 0, data.Length); }
                    string sid = System.Text.Encoding.ASCII.GetString(data);
                    if(pens.ContainsKey(idulong))
                        pens[idulong].FwStr = sid;
                    Console.WriteLine(sid);
                    //PenCtrl.Instance.AFAP_GetWifiSSID(id);
                }
                break;
            case AFE_EVENT_TYPE.AFE_GetBattery:
                {
                    AFEGetBattery_ obj2 = (AFEGetBattery_)eventObj;
                    byte val = obj2.val;
                    byte status = obj2.status;
                    String chargeStatus = "";
                    if (status == 1)
                        chargeStatus = "充電中";
                    else if (status == 2)
                        chargeStatus = "已充飽";
                    String text = chargeStatus + " " + val;
                    Console.WriteLine(text);
                }
                break;
            case AFE_EVENT_TYPE.AFE_ClearStorage:
                {
                    AFEClearStorage_ obj3 = (AFEClearStorage_)eventObj;
                    Console.WriteLine(String.Format("{0} ret={1}", id, obj3.bSuccess));
                    break;
                }
            case AFE_EVENT_TYPE.AFE_GetDots: //获取离线点位
                {
                    PenPrensenter mPenPrensenter = pens[Util.StringMac2Long(id)];
                    unsafe
                        {
                            AFEGetDots_ obj4 = (AFEGetDots_)eventObj;
                            Console.WriteLine("{0} {1} {2} {3}", obj4.readcnt, obj4.readedcnt, obj4.totalCnt, obj4.dotCnt);

                            if (obj4.dotCnt > 0)
                            {
                                for (int i = 0; i < obj4.dotCnt; i++)
                                {

                                    AFEDot_ dot = obj4.list[i];
                                    mPenPrensenter.cnt++;
                                    string txt = String.Format("{0} {1} Page={2} {3}", dot.X, dot.Y, dot.page, (int)dot.type);
                                    Console.WriteLine(txt);
                                }
                            }
                        if (obj4.readedcnt == obj4.totalCnt)
                        {
                            Console.WriteLine(String.Format("{0} sync done datacnt={1}", id, mPenPrensenter.cnt));
                        }

                    }
                }
                break;
            case AFE_EVENT_TYPE.AFE_GetStandbyTime:
                    AFEGetStandbyTime_ obj5 = (AFEGetStandbyTime_)eventObj;
                    string res1 = "" + obj5.min_powerdown;
                break;
            case AFE_EVENT_TYPE.AFE_SetStandbyTime:
                    AFESetStandbyTime_ obj6 = (AFESetStandbyTime_)eventObj;
                    string res2 = "" + obj6.bSuccess.ToString();
                break;
            case AFE_EVENT_TYPE.AFE_GetUSBReconnectMin:
                {
                    AFEGetUSBReconnectMin_ obj = (AFEGetUSBReconnectMin_)eventObj;
                    Console.WriteLine(obj.minute + "");
                }
                break;
            case AFE_EVENT_TYPE.AFE_SetUSBReconnectMin:
                {
                    AFESetUSBReconnectMin_ obj = (AFESetUSBReconnectMin_)eventObj;
                    Console.WriteLine(obj.bSuccess + "");
                }
                break;
        }
    }

    public void onAFPenUpdaterReport(ulong id, int status, int progress)
    {
        if (pens.ContainsKey(id))
            pens[id].onAFPenUpdaterReport( id,  status,  progress);
    }

    public void onMediaPlayerCallback(string macstr, AFE_MediaPlayer_EVENT_TYPE t, int state, byte[] data)
    {
        form.onMediaPlayerCallback(macstr, t, state, data);
    }
}
