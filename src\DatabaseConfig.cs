using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AFWifiServerDemo.src
{
    /// <summary>
    /// 数据库配置管理类
    /// 负责管理数据库连接字符串和相关配置
    /// </summary>
    public class DatabaseConfig
    {
        /// <summary>
        /// 默认的SQL Server连接字符串
        /// 注意：在生产环境中，建议将敏感信息存储在配置文件或环境变量中
        /// </summary>
        public static string DefaultConnectionString { get; private set; } =
            "Server=*************;Database=YouwoEduPlatfrom;User ID=sa_dev;Password=*************;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=false;TrustServerCertificate=True;";

        /// <summary>
        /// 数据库连接超时时间（秒）
        /// </summary>
        public static int ConnectionTimeout { get; set; } = 30;

        /// <summary>
        /// 命令执行超时时间（秒）
        /// </summary>
        public static int CommandTimeout { get; set; } = 60;

        /// <summary>
        /// 是否启用SQL日志记录
        /// </summary>
        public static bool EnableSqlLogging { get; set; } = true;

        /// <summary>
        /// 是否启用数据库连接池
        /// </summary>
        public static bool EnableConnectionPool { get; set; } = true;

        /// <summary>
        /// 设置自定义连接字符串
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        public static void SetConnectionString(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                throw new ArgumentException("连接字符串不能为空", nameof(connectionString));
            }
            DefaultConnectionString = connectionString;
        }

        /// <summary>
        /// 从配置文件或环境变量加载连接字符串
        /// </summary>
        /// <returns>连接字符串</returns>
        public static string GetConnectionString()
        {
            // 优先从环境变量获取
            string envConnectionString = Environment.GetEnvironmentVariable("DB_CONNECTION_STRING");
            if (!string.IsNullOrWhiteSpace(envConnectionString))
            {
                return envConnectionString;
            }

            // 如果环境变量不存在，使用默认连接字符串
            return DefaultConnectionString;
        }

        /// <summary>
        /// 验证连接字符串格式
        /// </summary>
        /// <param name="connectionString">要验证的连接字符串</param>
        /// <returns>是否有效</returns>
        public static bool ValidateConnectionString(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
                return false;

            try
            {
                // 基本的连接字符串格式验证
                return connectionString.Contains("Server=") && 
                       connectionString.Contains("Database=");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取数据库配置信息（用于日志记录，不包含敏感信息）
        /// </summary>
        /// <returns>配置信息字符串</returns>
        public static string GetConfigInfo()
        {
            var connectionString = GetConnectionString();
            var serverStart = connectionString.IndexOf("Server=");
            var serverEnd = connectionString.IndexOf(";", serverStart);
            var server = serverStart >= 0 && serverEnd > serverStart 
                ? connectionString.Substring(serverStart, serverEnd - serverStart)
                : "Unknown";

            var dbStart = connectionString.IndexOf("Database=");
            var dbEnd = connectionString.IndexOf(";", dbStart);
            var database = dbStart >= 0 && dbEnd > dbStart 
                ? connectionString.Substring(dbStart, dbEnd - dbStart)
                : "Unknown";

            return $"数据库配置 - {server}, {database}, 连接超时: {ConnectionTimeout}s, 命令超时: {CommandTimeout}s, SQL日志: {EnableSqlLogging}";
        }
    }
}
