{"format": 1, "restore": {"D:\\公司\\明鼎SDK\\AFWifiServerSDKDemo_X64_250723\\AFWifiServerSDKDemo\\AFWifiServerDemo\\AFWifiServerDemo.csproj": {}}, "projects": {"D:\\公司\\明鼎SDK\\AFWifiServerSDKDemo_X64_250723\\AFWifiServerSDKDemo\\AFWifiServerDemo\\AFWifiServerDemo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\公司\\明鼎SDK\\AFWifiServerSDKDemo_X64_250723\\AFWifiServerSDKDemo\\AFWifiServerDemo\\AFWifiServerDemo.csproj", "projectName": "AFWifiServerDemo", "projectPath": "D:\\公司\\明鼎SDK\\AFWifiServerSDKDemo_X64_250723\\AFWifiServerSDKDemo\\AFWifiServerDemo\\AFWifiServerDemo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\公司\\明鼎SDK\\AFWifiServerSDKDemo_X64_250723\\AFWifiServerSDKDemo\\AFWifiServerDemo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.nuget.org/api/v2/": {}}, "frameworks": {"net8.0-windows8.0": {"targetAlias": "net8.0-windows8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows8.0": {"targetAlias": "net8.0-windows8.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.1.1, )"}, "NLog": {"target": "Package", "version": "[5.3.2, )"}, "SqlSugar": {"target": "Package", "version": "[5.1.4.199, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}