using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AFWifiServerDemo.entitys
{
    public class MD_PaperMacMapping
    {
        [SugarColumn(IsPrimaryKey = true, ColumnName = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// 纸码，对应 dot.page
        /// </summary>
        public int PaperCode { get; set; }

       /// <summary>
        /// 笔Mac
        /// </summary>
        public string PMac { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public string UserId { get; set; }
        
        public DateTime CreateTime{ get; set; }
    }
}