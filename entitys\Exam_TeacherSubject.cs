﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace AFWifiServerDemo.entitys
{
    /// <summary>
    /// 教师对应科目表
    /// </summary>
    [Table("Exam_TeacherSubject")]
    [SugarTable("Exam_TeacherSubject")]
    public class Exam_TeacherSubject
    {

        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        [SugarColumn(IsPrimaryKey = true, ColumnName = "Id")]
        public String Id { get; set; }

        /// <summary>
        /// 教师ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 科目Id
        /// </summary>
        public string SubjectId { get; set; }

        /// <summary>
        /// 教师级别  2 任课教师; 3 该班当前科目下的其他教师，非任课教师;4 教研员
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 类型：  1 专课专练显示的科目  2 三个助手显示的科目
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 班级表
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 笔默认绑定
        /// </summary>
        public string PMac { get; set; }

    }
}
