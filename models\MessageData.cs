﻿using AFWifiServerDemo.src;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AFWifiServerDemo.models
{
    public class MessageData
    {
        /// <summary>
        /// RawId
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// KeyId
        /// </summary>
        public ulong Key => Id.StringToLong();

        /// <summary>
        /// MacAddress
        /// </summary>
        public string Mac => Id.ToMac();

        /// <summary>
        /// Event
        /// </summary>
        public int EventType { get; set; }

        /// <summary>
        /// EventData
        /// </summary>
        public object EventData { get; set; }
    }
}
