-- 创建纸张MAC地址映射表（更新版本，包含唯一性约束）
CREATE TABLE [dbo].[MD_PaperMacMapping] (
    [Id] NVARCHAR(50) NOT NULL PRIMARY KEY,           -- 主键ID
    [UserId] NVARCHAR(50) NOT NULL,                   -- 用户ID（学生ID）
    [PMac] NVARCHAR(50) NOT NULL,                     -- 笔MAC地址
    [PaperCode] INT NOT NULL,                         -- 纸张页码（对应dot.page）
    [CreateTime] DATETIME2(7) NOT NULL DEFAULT GETDATE(), -- 创建时间
    
    -- 唯一性约束：确保一个学生的一个页码只能绑定一支笔
    CONSTRAINT [UK_MD_PaperMacMapping_UserId_PaperCode] UNIQUE ([UserId], [PaperCode]),
    
    -- 创建索引以提高查询性能
    INDEX [IX_MD_PaperMacMapping_PMac_PaperCode] ([PMac], [PaperCode]),
    INDEX [IX_MD_PaperMacMapping_UserId] ([UserId]),
    INDEX [IX_MD_PaperMacMapping_PaperCode] ([PaperCode])
);

-- 添加表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'纸张MAC地址映射表，用于记录学生笔MAC地址与纸张页码的关联关系。确保一个学生的一个页码只能绑定一支笔。', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'MD_PaperMacMapping';

-- 添加字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD_PaperMacMapping', @level2type = N'COLUMN', @level2name = N'Id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'用户ID（学生ID）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD_PaperMacMapping', @level2type = N'COLUMN', @level2name = N'UserId';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'笔MAC地址', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD_PaperMacMapping', @level2type = N'COLUMN', @level2name = N'PMac';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'纸张页码，对应dot.page', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD_PaperMacMapping', @level2type = N'COLUMN', @level2name = N'PaperCode';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'MD_PaperMacMapping', @level2type = N'COLUMN', @level2name = N'CreateTime';

-- 如果表已存在，可以使用以下语句添加唯一约束
-- ALTER TABLE [dbo].[MD_PaperMacMapping] 
-- ADD CONSTRAINT [UK_MD_PaperMacMapping_UserId_PaperCode] UNIQUE ([UserId], [PaperCode]);

/*
业务规则说明：
1. 一个学生(UserId)的一个页码(PaperCode)只能绑定一支笔(PMac)
2. 同一支笔可以绑定多个不同的页码
3. 同一个页码可以被不同学生使用（但每个学生只能绑定一支笔到该页码）
4. 如果学生尝试用不同的笔写同一页码，系统会拒绝并给出警告

示例数据：
- 学生A + 页码1 + 笔MAC1 ✓ 允许
- 学生A + 页码2 + 笔MAC1 ✓ 允许（同一支笔，不同页码）
- 学生A + 页码1 + 笔MAC2 ✗ 拒绝（同一学生同一页码不能绑定不同笔）
- 学生B + 页码1 + 笔MAC2 ✓ 允许（不同学生可以使用同一页码）
*/
