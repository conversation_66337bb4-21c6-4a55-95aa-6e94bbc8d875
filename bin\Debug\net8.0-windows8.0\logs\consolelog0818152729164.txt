0818 15:27:29:171 1  xbbuilt: 2025-07-23_18:06:56
0818 15:27:29:206 1 xa built:<PERSON><PERSON> Jul  8 00:47:05 2025
0818 15:27:39:057 15 AFAP_free()
0818 15:27:39:096 12 crash    at System.Windows.Forms.NativeWindow.CreateHandle(CreateParams cp)
   at System.Windows.Forms.Control.CreateHandle()
   at System.Windows.Forms.TextBoxBase.CreateHandle()
   at System.Windows.Forms.Control.get_Handle()
   at System.Windows.Forms.Control.IHandle<Windows.Win32.Foundation.HWND>.get_Handle()
   at Windows.Win32.PInvoke.SendMessage[T](T hWnd, MessageId Msg, WPARAM wParam, LPARAM lParam)
   at Windows.Win32.PInvoke.SendMessage[THwnd,TLParam](THwnd hWnd, MessageId Msg, WPARAM wParam, TLParam& lParam)
   at System.Windows.Forms.TextBoxBase.GetSelectionStartAndLength(Int32& start, Int32& length)
   at System.Windows.Forms.TextBoxBase.OnHandleDestroyed(EventArgs e)
   at System.Windows.Forms.Control.WmDestroy(Message& m)
   at System.Windows.Forms.Control.WndProc(Message& m)
   at System.Windows.Forms.TextBoxBase.WndProc(Message& m)
   at System.Windows.Forms.TextBox.WndProc(Message& m)
   at System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   at System.Windows.Forms.NativeWindow.Callback(HWND hWnd, MessageId msg, WPARAM wparam, LPARAM lparam)
0818 15:27:39:097 12 创建窗口句柄时出错。
