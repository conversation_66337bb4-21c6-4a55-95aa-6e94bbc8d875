# AFWifiServerDemo - SqlSugar数据库集成

## 概述

本项目已成功集成SqlSugar ORM框架，用于访问SQL Server数据库。程序在原有的笔迹处理功能基础上，添加了完整的数据库操作功能。

## 新增功能

### 1. 数据库配置管理
- **DatabaseConfig类**: 管理数据库连接字符串和配置
- 支持环境变量配置（`DB_CONNECTION_STRING`）
- 提供连接字符串验证功能
- 安全的配置信息显示（隐藏敏感信息）

### 2. 数据库服务
- **DatabaseService类**: 封装SqlSugar的数据库操作
- 单例模式设计，确保连接复用
- 完整的CRUD操作支持
- SQL日志记录功能
- 异常处理和错误日志

### 3. 实体类支持
已为以下实体类添加SqlSugar特性：
- `Base_User`: 系统用户表
- `Exam_Student`: 学生信息表
- `Exam_TeacherSubject`: 教师科目表
- `MD_UserPenMapping`: 用户笔映射表

### 4. 数据库操作命令
程序提供以下键盘快捷键进行数据库操作：

#### 基本操作
- `T` - 测试数据库连接
- `U` - 创建测试用户
- `G` - 获取用户列表（分页显示）
- `D` - 删除用户（软删除）
- `F` - 根据用户名查找用户

#### 高级操作
- `M` - 创建笔用户映射
- `S` - 执行自定义SQL查询（支持SELECT和DML操作）

#### 原有功能
- `1` - 获取电池信息
- `2` - 获取固件版本
- `Z` - 同步所有离线数据
- `X` - 获取所有连接的设备
- `=` - 删除当前连接笔数据
- `V` - 启动服务器
- `B` - 停止服务器
- `H` - 更新固件
- `K` - 获取待机时间
- `L` - 设置关机时间
- `O` - 设置USB重连时间
- `P` - 获取USB重连时间
- `.` - 退出程序

## 数据库配置

### 默认连接字符串
```
Server=*************;Database=YouwoEduPlatfrom;User ID=sa_dev;Password=*************;Trusted_Connection=False;MultipleActiveResultSets=true;Max Pool Size=512;Min Pool Size=5;TrustServerCertificate=True;
```

### 环境变量配置（推荐）
为了提高安全性，建议使用环境变量设置数据库连接字符串：
```bash
set DB_CONNECTION_STRING="你的数据库连接字符串"
```

## 技术特性

### SqlSugar配置
- 数据库类型: SQL Server
- 自动关闭连接: 启用
- 主键初始化: 基于特性
- 连接池: 启用（最小5个，最大512个连接）
- SSL证书验证: 跳过（TrustServerCertificate=True）

### 日志功能
- SQL执行日志记录
- 参数详细记录
- 错误异常捕获
- NLog集成支持

### 数据库操作特性
- 异步操作支持
- 事务处理能力
- 分页查询支持
- 软删除实现
- 原生SQL执行支持

## 使用示例

### 1. 启动程序
```bash
dotnet run
```

### 2. 测试数据库连接
程序启动后按 `T` 键测试数据库连接。

### 3. 创建测试用户
按 `U` 键创建一个测试用户，系统会自动生成用户信息。

### 4. 查看用户列表
按 `G` 键查看当前数据库中的用户列表（分页显示，每页10条）。

### 5. 执行自定义SQL
按 `S` 键可以执行自定义SQL语句：
- 选择操作类型（查询或更新）
- 输入SQL语句
- 查看执行结果

## 注意事项

1. **网络连接**: 确保能够访问数据库服务器（*************）
2. **防火墙**: 检查防火墙设置，确保SQL Server端口（通常是1433）可访问
3. **数据库权限**: 确保提供的数据库用户具有足够的权限
4. **SSL证书**: 当前配置跳过SSL证书验证，生产环境建议启用
5. **连接池**: 已配置连接池，支持高并发访问

## 错误处理

程序包含完整的错误处理机制：
- 数据库连接失败时会显示详细错误信息
- SQL执行异常会被捕获并记录
- 网络超时会有相应的提示信息

## 扩展建议

1. **配置文件**: 考虑使用appsettings.json配置文件
2. **加密连接**: 生产环境建议启用SSL加密
3. **连接池监控**: 添加连接池状态监控
4. **数据验证**: 增强输入数据验证
5. **审计日志**: 添加数据库操作审计功能

## 依赖包

- SqlSugar 5.1.4.199
- Microsoft.Data.SqlClient 6.1.1
- NLog 5.3.2

## 编译和运行

程序已成功编译，可以直接运行。虽然有一些关于可空引用类型的警告，但不影响程序功能。

```bash
dotnet build  # 编译项目
dotnet run    # 运行项目
```
