using System;

namespace AFWifiServerDemo
{
    /// <summary>
    /// 页码映射功能测试类
    /// </summary>
    public class PageMappingTest
    {
        /// <summary>
        /// 测试页码映射功能
        /// </summary>
        public static void TestPageMapping()
        {
            Console.WriteLine("=== 页码映射功能测试 ===");
            Console.WriteLine("测试范围：100-1000 → 1-4循环模式");
            Console.WriteLine();

            // 测试几个关键点
            int[] testPages = { 100, 101, 102, 103, 104, 105, 200, 300, 400, 500, 600, 700, 800, 900, 1000 };
            
            Console.WriteLine("原始页码\t映射页码\t套数\t套内页码\t计算说明");
            Console.WriteLine(new string('-', 80));

            foreach (int originalPage in testPages)
            {
                // 计算映射逻辑
                int totalMappedPages = 320; // 80套 × 4页
                int pagesPerSet = 4;
                
                // 计算在映射范围内的索引 (0-319)
                int mappedIndex = (originalPage - 100) % totalMappedPages;
                
                // 计算套数 (1-80)
                int setNumber = mappedIndex / pagesPerSet + 1;
                
                // 计算套内页码 (1-4)
                int pageInSet = mappedIndex % pagesPerSet + 1;
                
                // 映射页码就是套内页码
                int mappedPage = pageInSet;
                
                string calculation = $"({originalPage}-100)%320={mappedIndex}, 套{setNumber}, 页{pageInSet}";
                
                Console.WriteLine($"{originalPage}\t\t{mappedPage}\t\t{setNumber}\t{pageInSet}\t\t{calculation}");
            }

            Console.WriteLine(new string('-', 80));
            Console.WriteLine("测试完成！");
            Console.WriteLine();
            
            // 验证循环模式
            Console.WriteLine("=== 验证循环模式 ===");
            Console.WriteLine("验证每4页为一个循环：");
            for (int i = 100; i <= 115; i++)
            {
                int mappedIndex = (i - 100) % 320;
                int mappedPage = mappedIndex % 4 + 1;
                Console.WriteLine($"页码{i} → 映射页码{mappedPage}");
            }
            
            Console.WriteLine();
            Console.WriteLine("验证320页后重新开始循环：");
            for (int i = 420; i <= 435; i++) // 320页后的测试
            {
                int mappedIndex = (i - 100) % 320;
                int mappedPage = mappedIndex % 4 + 1;
                Console.WriteLine($"页码{i} → 映射页码{mappedPage} (与页码{100 + mappedIndex}相同)");
            }
        }
    }
}
